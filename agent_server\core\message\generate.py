from enum import Enum
import asyncio
from typing import Any, List
from uuid import uuid4
from datetime import datetime, timezone

from agent_server.utils.redis_util import redis_client
from agent_server.core.message.transmitter import Transmitter
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.conversation import conversation_curd
from agent_server.core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationTable,
)
from agent_server.core.config.app_config import config
from agent_server.core.config.app_logger import logger


# 常量定义
CONVERSATION_TITLE_MAX_LENGTH = 20  # 会话标题最大长度
START_DELAY = 0.5  # 消息流开始延迟时间（秒）
MESSAGE_CONTENT_OFFSET = 15  # 消息内容偏移量，用于提取实际消息内容

class MessageSignal(str, Enum):
    """消息信号枚举类

    定义消息流中使用的特殊信号标识，用于控制消息生成和传输的状态
    """
    Error = "[[<<ERROR>>]]"    # 错误信号
    Start = "[[<<START>>]]"    # 开始信号
    Done = "[[<<DONE>>]]"      # 完成信号
    Cancel = "[[<<CANCEL>>]]"  # 取消信号


def create_error_message(error: str) -> str:
    """创建错误消息

    Args:
        error (str): 错误信息描述

    Returns:
        str: 格式化的错误消息
    """
    logger.debug(f"创建错误消息: {error}")
    return f"{MessageSignal.Error}: {error}"


def create_start_message() -> str:
    """创建开始消息

    Returns:
        str: 开始信号消息
    """
    logger.debug("创建开始消息")
    return MessageSignal.Start


def create_done_message() -> str:
    """创建完成消息

    Returns:
        str: 完成信号消息
    """
    logger.debug("创建完成消息")
    return MessageSignal.Done


def create_cancel_message(reason: str) -> str:
    """创建取消消息

    Args:
        reason (str): 取消原因

    Returns:
        str: 格式化的取消消息
    """
    logger.debug(f"创建取消消息: {reason}")
    return f"{MessageSignal.Cancel}: {reason}"


def check_existed(key: str) -> bool:
    """检查Redis中的键是否存在且有效

    Args:
        key (str): Redis键名

    Returns:
        bool: 键存在且有效返回True，否则返回False
    """
    logger.debug(f"检查Redis键是否存在: {key}")
    ttl = redis_client.client.ttl(key)
    # ttl == -2 -> key 不存在 (极少数 race 情况)， ttl == -1 -> 无过期
    if ttl in (-1, -2):
        logger.debug(f"Redis键不存在或已过期: {key}, ttl={ttl}")
        return False
    logger.debug(f"Redis键存在: {key}, ttl={ttl}")
    return True


def get_latest_chunk(key: str) -> str:
    """获取消息队列中最新的消息块

    Args:
        key (str): Redis列表键名

    Returns:
        str: 最新的消息块内容，如果不存在则返回None
    """
    logger.debug(f"获取最新消息块: {key}")
    result = redis_client.client.lindex(key, 0)
    logger.debug(f"获取到的最新消息块: {result}")
    return result


def push_message_chunk(key: str, message: str) -> None:
    """向消息队列推送新的消息块

    Args:
        key (str): Redis列表键名
        message (str): 要推送的消息内容
    """
    logger.debug(f"推送消息块到队列: {key}, 消息长度: {len(message)}")
    redis_client.client.lpush(key, message)


def get_all_chunks(key: str) -> List[str]:
    """获取消息队列中的所有消息块

    Args:
        key (str): Redis列表键名

    Returns:
        List[str]: 所有消息块的列表
    """
    logger.debug(f"获取所有消息块: {key}")
    result = redis_client.client.lrange(key, 0, -1)
    logger.debug(f"获取到的消息块数量: {len(result) if result else 0}")
    return result


def get_message_content(message: str) -> str:
    """从带有信号前缀的消息中提取实际内容

    Args:
        message (str): 完整的消息字符串

    Returns:
        str: 提取出的消息内容
    """
    content = message[MESSAGE_CONTENT_OFFSET:]
    logger.debug(f"提取消息内容，原长度: {len(message)}, 提取后长度: {len(content)}")
    return content


def getPreservedKey(key: str) -> str:
    """生成保存消息的Redis键名

    Args:
        key (str): 原始键名

    Returns:
        str: 带有_preserved后缀的键名
    """
    preserved_key = f"{key}_preserved"
    logger.debug(f"生成保存键名: {key} -> {preserved_key}")
    return preserved_key


async def message_generate(task_id: str, message_generator: Any) -> None:
    """异步消息生成器处理函数

    从消息生成器中逐个获取消息并推送到Redis队列中，支持取消信号中断

    Args:
        task_id (str): 任务ID，用作Redis队列的键名
        message_generator (Any): 异步消息生成器

    Raises:
        Exception: 消息生成过程中的任何异常都会被捕获并转换为错误消息
    """
    logger.info(f"开始消息生成任务: {task_id}")

    try:
        async for msg in message_generator:
            # 收到 cancel 信号中断生成
            head = get_latest_chunk(task_id)
            if head and head.startswith(MessageSignal.Cancel):
                logger.info(f"收到取消信号，中断消息生成: {task_id}")
                return

            push_message_chunk(key=task_id, message=msg)

    except Exception as e:
        error_msg = f"消息生成异常: {str(e)}"
        logger.error(f"任务 {task_id} 发生异常: {error_msg}", exc_info=True)
        push_message_chunk(key=task_id, message=create_error_message(str(e) + "\n"))
    finally:
        push_message_chunk(key=task_id, message=create_done_message())


async def run_generate_message_task(
    task_id: str,
    conversation_id: str,
    agent_code: str,
    message_generator_fn: Any,
) -> None:
    """运行消息生成任务

    创建并启动一个后台任务来处理消息生成，使用Transmitter来管理消息传输

    Args:
        task_id (str): 任务唯一标识符
        conversation_id (str): 会话ID
        agent_code (str): 智能体代码
        message_generator_fn (Any): 消息生成器函数，接收Transmitter参数

    Note:
        该函数会立即返回，消息生成在后台异步执行
    """
    logger.info(f"启动消息生成任务: task_id={task_id}, conversation_id={conversation_id}, agent_code={agent_code}")

    # 插入占位符，保证列表永远不为空
    push_message_chunk(key=task_id, message=create_start_message())

    async def task():
        """内部任务函数，负责实际的消息生成逻辑"""
        try:
            # 创建消息传输器
            message_id = uuid4().hex
            logger.debug(f"创建Transmitter: message_id={message_id}")

            transmitter = Transmitter(
                conversation_id=conversation_id,
                message_id=message_id,
                agent_code=agent_code,
            )

            # 开始消息生成
            await message_generate(
                task_id,
                message_generator_fn(transmitter),
            )

        except Exception as e:
            error_msg = f"任务执行异常: {str(e)}"
            logger.error(f"任务 {task_id} 执行失败: {error_msg}", exc_info=True)
            push_message_chunk(key=task_id, message=create_error_message(str(e)))

    # 后台运行 task，使用shield保护任务不被取消
    logger.debug(f"创建后台任务: {task_id}")
    generate_task = asyncio.create_task(task())
    asyncio.shield(generate_task)


def subscribe_message(task_id: str) -> str | None:
    """订阅消息队列中的消息

    使用阻塞式弹出操作从Redis队列中获取消息，同时将消息备份到保存队列中

    Args:
        task_id (str): 任务ID，用作Redis队列的键名

    Returns:
        str | None: 获取到的消息内容，超时或队列为空时返回None

    Note:
        该函数会阻塞等待直到有消息可用或超时
    """
    logger.debug(f"开始订阅消息: {task_id}, 超时时间: {config.message.waiting_timeout}秒")

    try:
        # 使用阻塞式右弹出操作获取消息
        result = redis_client.client.brpop(task_id, config.message.waiting_timeout)

        if result and result[1]:
            message = result[1]
            logger.debug(f"收到消息: {task_id}, 消息长度: {len(message)}")

            # 将生成的消息数据存在 key=task_id_preserved 中缓存
            # key=task_id 的值中只保存最新的数据，消息块消费后即被删除
            preserved_data_key = getPreservedKey(task_id)
            push_message_chunk(key=preserved_data_key, message=message)

            return message
        else:
            logger.debug(f"消息订阅超时或队列为空: {task_id}")
            return None

    except Exception as e:
        logger.error(f"订阅消息时发生异常: {task_id}, 错误: {str(e)}", exc_info=True)
        return None


class MessageStreamHandler:
    """消息流处理器，负责管理消息的生成和分发

    该类提供了完整的消息流处理功能，包括：
    - 获取已缓存的历史消息
    - 实时流式获取新消息
    - 消息过滤和状态判断
    - 异常处理和日志记录
    """

    def __init__(self, task_id: str, conversation_id: str):
        """初始化消息流处理器

        Args:
            task_id (str): 任务唯一标识符
            conversation_id (str): 会话ID
        """
        self.task_id = task_id
        self.conversation_id = conversation_id
        self.preserved_data_key = getPreservedKey(task_id)

        logger.info(f"创建消息流处理器: task_id={task_id}, conversation_id={conversation_id}")

    async def start(self):
        """启动消息流处理

        按顺序执行以下步骤：
        1. 等待启动延迟
        2. 获取已缓存的消息
        3. 流式获取新消息
        """

        # 等待启动延迟，确保消息生成任务已经开始
        await asyncio.sleep(START_DELAY)

        # 先获取已缓存的消息
        async for message in self.get_cached_messages():
            yield message

        # 然后流式获取新消息
        async for message in self.stream_new_messages():
            yield message

    async def get_cached_messages(self):
        """获取已缓存的消息

        从保存队列中获取所有已生成的消息，并按正确顺序返回

        Yields:
            str: 缓存的消息内容
        """

        all_created_messages = get_all_chunks(self.preserved_data_key)

        if all_created_messages:
            # 反转消息顺序，因为Redis列表是后进先出的
            all_created_messages.reverse()

            for message in all_created_messages:
                if self._should_skip_message(message):
                    continue

                if self._is_terminal_message(message):
                    return

                yield message
        else:
            logger.debug("没有找到缓存消息")

    def _should_skip_message(self, message: str) -> bool:
        """判断是否应该跳过消息

        Args:
            message (str): 消息内容

        Returns:
            bool: 应该跳过返回True，否则返回False
        """
        return message == MessageSignal.Start

    def _is_terminal_message(self, message: str) -> bool:
        """判断是否为终止消息

        Args:
            message (str): 消息内容

        Returns:
            bool: 是终止消息返回True，否则返回False
        """
        is_terminal = (message == MessageSignal.Done or
                      message.startswith(MessageSignal.Cancel))

        if is_terminal:
            logger.debug(f"检测到终止消息: {message}")

        return is_terminal

    async def stream_new_messages(self):
        """流式获取新消息

        持续监听消息队列，实时获取新生成的消息

        Yields:
            str: 新生成的消息内容

        Raises:
            Exception: 当收到错误信号时抛出异常
        """
        message_count = 0

        while True:
            try:
                # 在线程中执行阻塞的消息订阅操作
                message = await asyncio.to_thread(subscribe_message, self.task_id)

                if not message:
                    logger.debug(f"消息流结束（超时或队列为空）: {self.task_id}")
                    return

                message_count += 1

                # 跳过开始信号
                if self._should_skip_message(message):
                    continue

                # 检查终止信号
                if self._is_terminal_message(message):
                    # logger.info(f"收到终止信号，结束消息流: {self.task_id}")
                    return

                # 检查错误信号
                if message.startswith(MessageSignal.Error):
                    error_content = get_message_content(message)
                    logger.error(f"收到错误信号: {self.task_id}, 错误内容: {error_content}")
                    raise Exception(error_content)

                # logger.debug(f"返回新消息: {self.task_id}, 长度: {len(message)}")
                yield message

            except Exception as e:
                logger.error(f"流式获取消息时发生异常: {self.task_id}, 错误: {str(e)}", exc_info=True)
                raise


async def create_or_update_conversation(
    message: str,
    conversation: ConversationTable | None,
    agent_code: str,
    user_id: int | str,
) -> tuple[str, str]:
    """创建或更新会话记录

    根据是否存在现有会话，创建新会话或更新现有会话的任务ID

    Args:
        message (str): 用户消息内容，用于生成会话标题
        conversation (ConversationTable | None): 现有会话对象，None表示创建新会话
        agent_code (str): 智能体代码
        user_id (int | str): 用户ID

    Returns:
        tuple[str, str]: 返回(会话ID, 任务ID)的元组

    Raises:
        Exception: 数据库操作异常
    """
    # 生成唯一的任务ID
    task_id = f"message-task:{uuid4().hex}"
    logger.info(f"创建/更新会话: user_id={user_id}, agent_code={agent_code}, task_id={task_id}")

    conversation_id = None
    if conversation:
        conversation_id = conversation.id

    try:
        async with db_manager.session() as session:
            if not conversation:
                # 创建新会话
                title = message[:CONVERSATION_TITLE_MAX_LENGTH]

                new_conversation = ConversationCreate(
                    user_id=user_id,
                    title=title,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    current_agent_code=agent_code,
                    task_id=task_id,
                )

                new_conversation = await conversation_curd.create(
                    db=session,
                    obj_input=new_conversation,
                )
                conversation_id = new_conversation.id

            else:
                # 更新现有会话的任务ID
                await conversation_curd.update(
                    db=session,
                    db_obj=conversation,
                    obj_input={"task_id": task_id},
                )

            return conversation_id, task_id

    except Exception as e:
        logger.error(f"创建/更新会话失败: user_id={user_id}, agent_code={agent_code}, 错误: {str(e)}", exc_info=True)
        raise


